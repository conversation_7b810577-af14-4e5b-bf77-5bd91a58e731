<script setup lang="ts">
import { onActivated, reactive } from 'vue';
import { useRouter } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import TableScroll from '@/components/common/table-scroll.vue';
import PaginatorQt from '@/components/common/paginator-qt.vue';
import TitleButton from './components/title-button.vue';
import SearchSimple from './components/search-simple.vue';
import QueryTable from './components/query-table.vue';
import Modals from './components/modals.vue';

import type { ArticleVO } from '^/types/information-mgt';


import { ListApi } from '@/api/information-mgt/list';
import { useSider } from '@/uses/sider';
import { useQueryTable } from '@/uses/query-table';
import { useTableLoader } from '@/uses/table-loader';

import { handleListParams } from './helps/handle-api-data';
import { createCancelReleaseModel, createDeleteModel, createSearchSimpleModel, createTopModel } from './helps/models';
import { push } from '@/helps/navigation';
import { RouterName as RN } from '@/config/router';
import { WarningModelType } from '@/helps/warning-model';


defineOptions({
  name: 'InformationMgtList',
});


const router = useRouter();


const { getMenuName } = useSider();


const loadData = useTableLoader(ListApi<ArticleVO>, handleListParams);

const deleteModel = reactive(createDeleteModel());
const cancelReleaseModel = reactive(createCancelReleaseModel());
const topModel = reactive(createTopModel());


const qt = useQueryTable({
  load: loadData,
  simpleSearchModel: createSearchSimpleModel(),
});


const toAddPage = () => {
  push(router, {
    name: RN.InformationMgtAdd,
  });
};

const toEditPage = (id: number) => {
  push(router, {
    name: RN.InformationMgtEdit,
    params: { id },
  });
};

const toDetailPage = (id: number) => {
  push(router, {
    name: RN.InformationMgtDetail,
    params: { id },
  });
};

const onShowModal = (model:WarningModelType, id:number) => {
  Object.assign(model, {
    visible: true,
    id,
  });
};


const onSearch = () => {
  qt.search();
};


onActivated(() => {
  qt.load();
});

</script>

<template>
  <TitleBar :title="getMenuName((SMC) => SMC.InformationMgt)">
    <template #right>
      <TitleButton
        @on-add="toAddPage"
      />
    </template>
  </TitleBar>

  <SearchSimple
    v-model="qt.simpleSearchModel"
    @on-search="onSearch"
  />

  <TableScroll>
    <QueryTable
      :data="qt.table.data"
      :loading="qt.table.loading"
      @on-view="toDetailPage"
      @on-edit="toEditPage"
      @on-delete="(id)=>onShowModal(deleteModel, id)"
      @on-cancel-release="(id)=>onShowModal(cancelReleaseModel, id)"
      @on-top="(id)=>onShowModal(topModel, id)"
    />

    <template #paginator>
      <PaginatorQt
        :query-table="qt"
      />
    </template>
  </TableScroll>


  <Modals
    v-model:delete-model="deleteModel"
    v-model:cancel-release-model="cancelReleaseModel"
    v-model:top-model="topModel"
    @on-reload="qt.search"
  />
</template>
