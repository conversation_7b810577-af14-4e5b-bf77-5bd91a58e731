export default {
  title: {
    add: '新建资讯',
    edit: '编辑',
    detail: '详情',
  },

  label: {
    type: '类型',
    publishTime: '发布时间',
    status: '状态',

    title: '标题',

    coverImg: '封面图',
    isExternalLink: '是否外链',
    externalLink: '外链',
    content: '内容',
    isScheduleRelease: '是否定时发布',
    schedulerTime: '定时发布时间',

    top: '置顶',

  },

  columns: {
    title: '@:informationMgt.label.title',
    type: '@:informationMgt.label.type',
    status: '@:informationMgt.label.status',

    operatorAndTime: '操作人/时间',

  },

  text: {
    coverImg: '支持jpg, png等格式文件上传，分辨率为936*526，文件大小不超过10MB',
    topTips: '[置顶] {start} ~ {end}',
  },

  placeholder: {
    externalLink: '外链有值时，内容可为空',
  },

  hint: {
    cancelReleaseSuccessfully: '取消发布成功',
  },


  action: {
    add: '@:informationMgt.title.add',
    view: '查看',
    edit: '@:common.action.edit',
    delete: '@:common.action.delete',
    top: '置顶设置',
    cancelRelease: '取消发布',
  },

  modal: {
    delete: {
      title: '@:informationMgt.action.delete',
      content: '是否确认删除此资讯内容？',
    },
    cancelRelease: {
      title: '@:informationMgt.action.cancelRelease',
      content: '是否确认取消发布此资讯内容？',
    },

    top: {
      title: '@:informationMgt.action.top',
      label: {
        isTop: '是否置顶',
        topTime: '置顶时间',
      },
    },
  },

};
