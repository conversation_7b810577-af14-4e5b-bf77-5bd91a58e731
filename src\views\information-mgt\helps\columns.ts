import type { ColumnsType } from '^/types/columns';

import { namespaceT } from '@/helps/namespace-t';

export const createColumns = (): ColumnsType[] => {
  const t = namespaceT('informationMgt.columns');
  const tc = namespaceT('common.table');

  return [
    {
      title: tc('serial'),
      type: 'index',
      align: 'center',
      width: 60,
    },

    // 标题
    {
      title: t('title'),
      slot: 'title',
      minWidth: 100,
    },

    // 类型
    {
      title: t('type'),
      slot: 'type',
      minWidth: 100,
    },

    // 状态
    {
      title: t('status'),
      slot: 'status',
      minWidth: 100,
    },

    // 操作人/时间
    {
      title: t('operatorAndTime'),
      slot: 'operatorAndTime',
      minWidth: 100,
    },


    // 操作
    {
      title: tc('operation'),
      slot: 'operation',
      width: 180,
    },
  ];
};
