import { namespaceT } from '@/helps/namespace-t';
import { createDateTimeRule, createInputRules, createSelectRules } from '@/helps/rules';

export const createSubmitFormRules = () => {
  const t = namespaceT('informationMgt');
  return {
    title: [
      createInputRules(),
    ],
    type: [
      createSelectRules(),
    ],
    urlInd: [
      createSelectRules(),
    ],
    content: [
      createInputRules(),
    ],
    url: [
      createInputRules(),
    ],
    scheduledReleaseInd: [
      createSelectRules(),
    ],
    releaseTime: [
      createSelectRules({ type: 'date' }),
      createDateTimeRule({ name: t('label.schedulerTime') }),
    ],
  };
};
