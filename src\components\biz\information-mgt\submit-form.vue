<script setup lang="ts">
import { ref, watch, nextTick } from 'vue';
import { isBefore, subDays } from 'date-fns';

import PimaInput from '@/components/common/pima-input.vue';
import SelectArticleType from '../select/article-type.vue';
import RadioYoN from '@/components/biz/radio/y-o-n.vue';
import PimaRemoteEditor from '@/components/common/remote/pima-remote-editor.vue';
import UploadFile from '../upload-image';
import PickerDate from '@/components/common/picker/date.vue';

import type { ArticleDTO } from '^/types/information-mgt';

import { RelateType } from '@/consts/relate-type';
import { isN, isY } from '@/helps/y-o-n';
import { namespaceT } from '@/helps/namespace-t';
import { formScrollIntoError } from '@/utils/form-scroll-into-error';
import { createSubmitFormRules } from './helps/rules';


const model = defineModel<ArticleDTO>();

const LABEL_WIDTH = 120;


const t = namespaceT('informationMgt');
const td = namespaceT('dateFormat');
const tc = namespaceT('common');
const rules = createSubmitFormRules();
const fileInfo = {
  accept: 'image/*',
  access: ['.jpg', '.png'],
  relateType: RelateType.INFOPORTAL_ARTICLE_COVER,
  copys: 0,
  tips: t('text.coverImg'),
  limitSize: 10,
  isMultiple: false,
};

const formRef = ref();

/** 时间不小于当天时间 */
const disabledDate = (date:Date) => {
  return date && isBefore(date, subDays(new Date(), 1));
};


const validate = async () => {
  const flag = await formRef.value.validate();
  if (!flag) {
    formScrollIntoError(formRef.value);
  }

  return flag;
};

const validateField = async <T=void>(fieldName:string, callback:()=>T = (() => null)) => {
  await formRef.value.validateField(fieldName, callback);
};

const resetFields = () => {
  formRef.value.resetFields();
};


defineExpose({
  validate,
  validateField,
  resetFields,
});

watch(() => model.value.content, (newVal, oldVal) => {
  if (oldVal !== undefined) {
    nextTick(() => {
      formRef.value.validateField('content');
    });
  }
});

</script>


<template>
  <Form
    ref="formRef"
    class="pima-form"
    :model="model"
    :rules="rules"
    :label-width="LABEL_WIDTH"
  >
    <!-- 标题 -->
    <Row :gutter="80">
      <Col
        :span="12"
      >
        <FormItem
          prop="title"
          :label="t('label.title')"
        >
          <PimaInput
            v-model.trim="model.title"
            :placeholder="tc('placeholder.input')"
            :is-limit="false"
          />
        </FormItem>
      </Col>
    </Row>

    <!-- 类型 -->
    <Row :gutter="80">
      <Col
        :span="12"
      >
        <FormItem
          prop="type"
          :label="t('label.type')"
        >
          <SelectArticleType
            v-model="model.type"
            :placeholder="tc('placeholder.select',{name: t('label.type')})"
          />
        </FormItem>
      </Col>
    </Row>

    <!-- 封面图 -->
    <Row :gutter="80">
      <Col
        :span="12"
      >
        <FormItem
          :label="t('label.coverImg')"
        >
          <UploadFile
            v-model="model.coverFiles"
            in-form
            v-bind="fileInfo"
          />
        </FormItem>
      </Col>
    </Row>

    <!-- 是否外链 -->
    <Row :gutter="80">
      <Col
        :span="12"
      >
        <FormItem
          prop="urlInd"
          :label="t('label.isExternalLink')"
        >
          <RadioYoN
            v-model="model.urlInd"
          />
        </FormItem>
      </Col>
    </Row>

    <!-- 外链 -->
    <Row
      v-show="isY(model.urlInd)"
      :gutter="80"
    >
      <Col
        :span="12"
      >
        <FormItem
          prop="url"
          :label="t('label.externalLink')"
          :rules="isY(model.urlInd) ? rules.url :{}"
        >
          <PimaInput
            v-model.trim="model.url"
            :placeholder="t('placeholder.externalLink')"
            :is-limit="false"
          />
        </FormItem>
      </Col>
    </Row>

    <!-- 内容 -->
    <Row
      v-show="isN(model.urlInd)"
      :gutter="80"
    >
      <Col
        :span="24"
      >
        <FormItem
          prop="content"
          :label="t('label.content')"
          :rules="isN(model.urlInd) ? rules.content :{}"
        >
          <PimaRemoteEditor
            v-model.trim="model.content"
            :height="300"
          />

          <Input
            v-show="false"
            v-model.trim="model.content"
          />
        </FormItem>
      </Col>
    </Row>

    <!-- 是否定时发布 -->
    <Row :gutter="80">
      <Col
        :span="12"
      >
        <FormItem
          prop="scheduledReleaseInd"
          :label="t('label.isScheduleRelease')"
        >
          <RadioYoN
            v-model.trim="model.scheduledReleaseInd"
          />
        </FormItem>
      </Col>
    </Row>

    <!-- 定时发布时间 -->
    <Row
      v-if="isY(model.scheduledReleaseInd)"
      :gutter="80"
    >
      <Col
        :span="12"
      >
        <FormItem
          prop="releaseTime"
          :label="t('label.schedulerTime')"
        >
          <PickerDate
            v-model="model.releaseTime"
            type="datetime"
            :disabled-date="disabledDate"
            :format="td('dateTime')"
          />
        </FormItem>
      </Col>
    </Row>
  </Form>
</template>
