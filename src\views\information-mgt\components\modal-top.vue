<!-- eslint-disable @typescript-eslint/no-unused-expressions -->
<script lang="ts" setup>
import { reactive, ref, nextTick } from 'vue';

import PimaModal from '@/components/common/pima-modal.vue';
import RadioYoN from '@/components/biz/radio/y-o-n.vue';
import SelectDateRange from '@/components/common/select-date/select-date-range.vue';

import type { TopApiDTO } from '^/types/information-mgt';

import { DetailApi } from '@/api/information-mgt/detail';
import { isY } from '@/helps/y-o-n';
import { openToastError } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';
import { createSelectRules } from '@/helps/rules';
import { isNothing } from '@/utils/is';
import { executeWithDelay } from '@/utils/execution-with-delay';

import { createTopFormModels } from '../helps/models';
import { handleDetailForTop } from '../helps/handle-api-data';
import { addDays, isAfter, isBefore, subDays } from 'date-fns';


interface Props {
  id?:number;
}

const props = withDefaults(defineProps<Props>(), {
  id: undefined,
});

const emit = defineEmits<{
  'on-confirm': [model:TopApiDTO],
  'on-cancel': [],
}>();

const LABEL_WIDTH = 120;
const t = namespaceT('informationMgt.modal.top');
const tc = namespaceT('common');

const formRef = ref();
const loading = ref(false);
const model = reactive<TopApiDTO>(createTopFormModels());

const onCancel = () => {
  emit('on-cancel');
};

const onConfirm = async () => {
  const valid = await formRef.value.validate();
  if (!valid) {
    return;
  }

  emit('on-confirm', model);
};

const rules = {
  hotInd: [
    createSelectRules(),
  ],
  hotTime: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (isNothing(model.hotStartTime) || isNothing(model.hotEndTime)) {
          callback(tc('error.thisFieldMustBeSelected'));
          return;
        }
        callback();
      },
      trigger: 'change',
    },
  ],
};


const fetchDetail = async () => {
  try {
    loading.value = true;
    const api = new DetailApi({ id: props.id });
    const res = await api.sendWithSpecifyType();
    Object.assign(model, handleDetailForTop(res));
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
};


const onVisibleChange = (visible: boolean) => {
  if (!visible) {
    executeWithDelay(async () => {
      Object.assign(model, createTopFormModels());
      await nextTick();
      formRef.value.resetFields();
    });
  } else {
    props.id && fetchDetail();
  }
};

const minDateDisabled = (date: Date) => {
  const now = subDays(new Date(), 1);
  const maxDate = isNothing(model.hotEndTime) ? null : new Date();

  if (maxDate) {
    return isBefore(date, now) && isBefore(addDays(date, 1), maxDate);
  }

  return isBefore(date, now);
};

const maxDateDisabled = (date: Date) => {
  const now = subDays(new Date(), 1);
  const minDate = isNothing(model.hotStartTime) ? null : new Date(model.hotStartTime);


  if (minDate) {
    return isBefore(date, now) || isBefore(date, minDate);
  }

  return isBefore(date, now);
};


</script>


<template>
  <PimaModal
    :width="480"
    v-bind="$attrs"
    @cancel="onCancel"
    @confirm="onConfirm"
    @on-visible-change="onVisibleChange"
  >
    <Spin
      v-if="loading"
      class="pima-spin"
      fix
      large
    />
    <Form
      ref="formRef"
      class="pima-form full-width"
      :model="model"
      :rules="rules"
      :label-width="LABEL_WIDTH"
    >
      <FormItem
        prop="hotInd"
        :label="t('label.isTop')"
      >
        <RadioYoN v-model="model.hotInd" />
      </FormItem>

      <FormItem
        v-if="isY(model.hotInd)"
        prop="hotTime"
        :label="t('label.topTime')"
      >
        <SelectDateRange
          v-model:start-date="model.hotStartTime"
          v-model:end-date="model.hotEndTime"
          :min-disable-date="minDateDisabled"
          :max-disable-date="maxDateDisabled"
          transfer
        />
      </FormItem>
    </Form>
  </PimaModal>
</template>
