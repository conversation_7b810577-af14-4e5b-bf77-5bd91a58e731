<script setup lang="ts">
import { ref, reactive, onBeforeMount } from 'vue';
import { useRouter, useRoute } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import SubmitForm from '@/components/biz/information-mgt/submit-form.vue';

import { DetailApi } from '@/api/information-mgt/detail';
import { EditApi } from '@/api/information-mgt/edit';
import { goBack } from '@/helps/navigation';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';

import { createArticleDTO } from '../../helps/models';
import { handleSubmitFormData, handleDetailData } from '../../helps/handle-api-data';


const router = useRouter();
const route = useRoute();

const t = namespaceT('informationMgt');
const tc = namespaceT('common');

const formRef = ref();
const loading = ref(false);
const saving = ref(false);
const model = reactive(createArticleDTO());


const onSave = async () => {
  try {
    saving.value = true;
    const valid = await formRef.value.validate();
    if (!valid) {
      return;
    }

    const api = new EditApi({ id: route.params.id });
    api.data = {
      ...handleSubmitFormData(model),
    };
    await api.send();

    openToastSuccess(tc('hint.handleSuccess'));
    goBack(router);
  } catch (error) {
    openToastError(error.message);
  } finally {
    saving.value = false;
  }
};

const fetchDetail = async () => {
  try {
    loading.value = true;
    const api = new DetailApi({ id: route.params.id });
    const res = await api.sendWithSpecifyType();
    Object.assign(model, handleDetailData(res));
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  fetchDetail();
});

</script>


<template>
  <TitleBar
    :title="t('title.edit')"
    go-back
    @go-back="goBack(router)"
  />

  <WrapperForm
    :loading="loading"
    class="px-60 mb-80 mt-20"
  >
    <SubmitForm
      ref="formRef"
      v-model="model"
    />

    <template #action>
      <Button
        size="large"
        type="default"
        class="pima-btn mr-15"
        @click="goBack(router)"
      >
        {{ tc('action.cancel') }}
      </Button>

      <Button
        v-if="$can((P)=>P.InformationMgt.Edit)"
        type="primary"
        size="large"
        class="pima-btn"
        :loading="saving || loading"
        @click="onSave"
      >
        {{ tc('action.save') }}
      </Button>
    </template>
  </WrapperForm>
</template>
