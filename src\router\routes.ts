import type { RouteRecordRaw } from 'vue-router';
import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName as RN } from '@/config/router';
import { SiderMenuCodes as SMC } from '@/config/sider-menu';
import { Auth } from '@/config/auth';

import TheRoot from '@/components/the-root.vue';


export const routes = Object.freeze<RouteRecordRaw[]>([
  {
    path: PUBLIC_PATH,
    name: RN.Root,
    component: TheRoot,
    children: [

      /** 资讯管理 - 列表 */
      {
        path: 'information-mgt',
        name: RN.InformationMgt,
        component: () => import('@/views/information-mgt'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.InformationMgt],
          baseAuthCodes: [Auth.InformationMgt.View],
        },
      },

      /** 资讯管理 - 详情 */
      {
        path: 'information-mgt/:id/detail/',
        name: RN.InformationMgtDetail,
        component: () => import('@/views/information-mgt/children/detail'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.InformationMgt],
          baseAuthCodes: [Auth.InformationMgt.View],
        },
      },

      /** 资讯管理 - 新增 */
      {
        path: 'information-mgt/add',
        name: RN.InformationMgtAdd,
        component: () => import('@/views/information-mgt/children/add'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.InformationMgt],
          baseAuthCodes: [Auth.InformationMgt.Add],
        },
      },

      /** 资讯管理 - 编辑 */
      {
        path: 'information-mgt/:id/edit',
        name: RN.InformationMgtEdit,
        component: () => import('@/views/information-mgt/children/edit'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.InformationMgt],
          baseAuthCodes: [Auth.InformationMgt.Edit],
        },
      },


      {
        path: 'forbidden',
        name: RN.Forbidden,
        component: () => import('@/views/forbidden.vue'),
      },
    ],
  },
]);
