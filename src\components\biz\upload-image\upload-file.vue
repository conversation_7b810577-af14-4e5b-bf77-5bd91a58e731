<script lang="ts" setup>
import { computed, getCurrentInstance, watch, ref } from 'vue';

import { UploadApi } from '@/api/dfs/upload';
import { parentEmitter } from '@/helps/parent-emitter';
import { RelateType } from '@/consts/relate-type';
import { openToastError } from '@/helps/toast';

import NotUploaded from './components/not-uploaded.vue';
import HaveUploaded from './components/have-uploaded.vue';
import { AttachmentVO } from '^/types/attachment';


const props = withDefaults(defineProps<{
  inForm: boolean,
  modelValue: Array<AttachmentVO>,
  accept: string,
  access: Array<unknown>,
  copys: number,
  tips?: string,
  relateType: RelateType,
  width?: number,
  height?: number,
  isMultiple?: boolean,
}>(), {
  inForm: false,
  modelValue: () => [],
  accept: '',
  access: () => [],
  copys: 0,
  tips: '',
  width: 0,
  height: 0,
  isMultiple: true,
});

const emit = defineEmits([
  'update:modelValue',
]);

const vm = getCurrentInstance();

const isEmpty = computed(() => !props.modelValue?.length);

const fileList = ref(props.modelValue);
const uploading = ref(false);

function onDel(idx) {
  fileList.value.splice(idx, 1);
  emit('update:modelValue', fileList.value);
}

async function onUpload(file) {
  try {
    uploading.value = true;
    const api = new UploadApi();
    api.params = {
      relateType: props.relateType,
    };

    const formData = new FormData();
    formData.append('fileData', file);
    if (props.width && props.height) {
      formData.append('customWidth', String(props.width));
      formData.append('customHeight', String(props.height));
    }
    api.data = formData;
    const res = await api.send();
    if (props.isMultiple) {
      fileList.value.push(res.model);
    } else {
      fileList.value = [res.model];
    }
    emit('update:modelValue', fileList.value);
  } catch (error) {
    openToastError(error.message);
    throw error;
  } finally {
    uploading.value = false;
  }
}

function openWatchIfNeed() {
  if (props.inForm) {
    watch(() => props.modelValue, (val) => {
      parentEmitter(vm.proxy, 'FormItem', 'on-form-change', val);
    });
  }
}

openWatchIfNeed();

</script>


<template>
  <div>
    <NotUploaded
      :loading="uploading"
      :accept="accept"
      :access="access"
      :copys="copys"
      :tips="tips"
      :length="modelValue?.length"
      v-bind="$attrs"
      @on-upload="onUpload"
    />

    <div
      v-if="!isEmpty"
    >
      <HaveUploaded
        v-for="(item, idx) in modelValue"
        :key="`file-${idx}`"
        :file-url="item.filePath.normal"
        @on-delete="onDel(idx)"
      />
    </div>
  </div>
</template>
