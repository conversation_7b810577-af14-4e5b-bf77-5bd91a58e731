<script lang="ts" setup>
import { onBeforeMount, getCurrentInstance } from 'vue';

import type { InformationMgtListItemType } from '^/types/information-mgt';

import CTable from '@/components/common/c-table';
import TableActionWrapper from '@/components/common/table-action-wrapper.vue';
import TableAction from '@/components/common/table-action';
import DefaultText from '@/components/common/default-text.vue';

import { isY } from '@/helps/y-o-n';
import { namespaceT } from '@/helps/namespace-t';

import { createColumns } from '../helps/columns';
import { useArticleStatusStore } from '@/store/data-tags/article-status';
import { useArticleTypeStore } from '@/store/data-tags/article-type';
import { formateToDate } from '@/helps/formate-to-date-type';

defineProps<{
  data: InformationMgtListItemType[];
  loading: boolean;
}>();

const emit = defineEmits<{
  'on-view': [id:number],
  'on-top': [id:number],
  'on-edit': [id:number],
  'on-cancel-release': [id:number]
  'on-delete': [id:number]
}>();


const vm = getCurrentInstance();
const t = namespaceT('informationMgt');
const columns = createColumns();


const statusStore = useArticleStatusStore();
const typeStore = useArticleTypeStore();


const can = (type: string) => {
  return vm.proxy.$can((P) => P.InformationMgt[type]);
};


const actions = [
  {
    label: t('action.view'),
    triggerEvent: (row:InformationMgtListItemType) => emit('on-view', row.id),
  },
  {
    label: t('action.top'),
    triggerEvent: (row:InformationMgtListItemType) => emit('on-top', row.id),
    can: (row : InformationMgtListItemType) => isY(row.topSettingableInd) && can('Top'),
  },
  {
    label: t('action.edit'),
    triggerEvent: (row:InformationMgtListItemType) => emit('on-edit', row.id),
    can: (row : InformationMgtListItemType) => isY(row.editableInd) && can('Edit'),
  },

  {
    label: t('action.cancelRelease'),
    triggerEvent: (row: InformationMgtListItemType) => emit('on-cancel-release', row.id),
    can: (row: InformationMgtListItemType) => isY(row.cancelReleaseableInd) && can('Cancel'),
  },
  {
    label: t('action.delete'),
    triggerEvent: (row: InformationMgtListItemType) => emit('on-delete', row.id),
    can: (row: InformationMgtListItemType) => isY(row.deletableInd) && can('Remove'),
  },
];


onBeforeMount(() => {
  statusStore.loadDataIfNeeded();
  typeStore.loadDataIfNeeded();
});

</script>


<template>
  <CTable
    :columns="columns"
    :data="data"
    :loading="loading"
  >
    <!-- 标题 -->
    <template #title="{ row }">
      <div
        v-if="isY(row.hotInd)"
        class="warning-text"
      >
        {{ t('text.topTips',{start:formateToDate(row.hotStartTime),end:formateToDate(row.hotEndTime)}) }}
      </div>
      <DefaultText :text="row.title" />
    </template>

    <!-- 类型 -->
    <template #type="{ row }">
      <DefaultText :text="typeStore.getTextByCode(row.type)" />
    </template>

    <!-- 状态 -->
    <template #status="{ row }">
      <DefaultText :text="statusStore.getTextByCode(row.status)" />
      <br>
      <DefaultText :text="formateToDate(row.releaseTime,'dateTime')" />
    </template>


    <!-- 操作人/时间 -->
    <template #operatorAndTime="{ row }">
      <DefaultText :text="row.updUserName" />
      <br>
      <DefaultText :text="formateToDate(row.updTime,'dateTime')" />
    </template>


    <!-- 操作 -->
    <template #operation="{ row }">
      <TableActionWrapper>
        <TableAction
          :row-data="row"
          :limit="3"
          :actions="actions"
        />
      </TableActionWrapper>
    </template>
  </CTable>
</template>
