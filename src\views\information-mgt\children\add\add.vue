<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import SubmitForm from '@/components/biz/information-mgt/submit-form.vue';

import { namespaceT } from '@/helps/namespace-t';
import { goBack } from '@/helps/navigation';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { createArticleDTO } from '../../helps/models';
import { AddApi } from '@/api/information-mgt/add';
import { handleSubmitFormData } from '../../helps/handle-api-data';


const router = useRouter();

const t = namespaceT('informationMgt');
const tc = namespaceT('common');

const formRef = ref();
const saving = ref(false);
const model = reactive(createArticleDTO());


const onSave = async () => {
  try {
    saving.value = true;
    const valid = await formRef.value.validate();
    if (!valid) {
      return;
    }

    const api = new AddApi();
    api.data = {
      ...handleSubmitFormData(model),
    };
    await api.send();

    openToastSuccess(tc('hint.handleSuccess'));
    goBack(router);
  } catch (error) {
    openToastError(error.message);
  } finally {
    saving.value = false;
  }
};

</script>


<template>
  <TitleBar
    :title="t('title.add')"
    go-back
    @go-back="goBack(router)"
  />

  <WrapperForm
    class="px-60 mb-80 mt-20"
  >
    <SubmitForm
      ref="formRef"
      v-model="model"
    />

    <template #action>
      <Button
        size="large"
        type="default"
        class="pima-btn mr-15"
        @click="goBack(router)"
      >
        {{ tc('action.cancel') }}
      </Button>

      <Button
        v-if="$can((P)=>P.InformationMgt.Add)"
        type="primary"
        size="large"
        class="pima-btn mr-12"
        :loading="saving"
        @click="onSave"
      >
        {{ tc('action.save') }}
      </Button>
    </template>
  </WrapperForm>
</template>
