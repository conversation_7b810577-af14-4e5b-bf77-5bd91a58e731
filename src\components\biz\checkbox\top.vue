<script setup lang="ts">
import { YoN } from '@/consts/y-o-n';
import { namespaceT } from '@/helps/namespace-t';

const t = namespaceT('informationMgt.label');


</script>


<template>
  <div class="pima-checkbox-group">
    <Checkbox
      v-bind="$attrs"
      :true-value="YoN.Y"
      :false-value="undefined"
    >
      <span>{{ t('top') }}</span>
    </Checkbox>
  </div>
</template>


<style lang="less" scoped>
.pima-checkbox-group{
  display: flex;
  align-items: center;
  height: 32px;
}

</style>
