import { isBefore } from 'date-fns';
import { namespaceT } from './namespace-t';


const tm = namespaceT('common.error');

export function createInputRules({
  type = 'string',
  message = tm('thisFieldIsRequired'),
} = {
  type: 'string',
  message: tm('thisFieldIsRequired'),
}) {
  return {
    type,
    message,
    required: true,
    trigger: 'blur',
  };
}

export function createSelectRules({
  type = 'string',
  message = tm('thisFieldMustBeSelected'),
} = {
  type: 'string',
  message: tm('thisFieldMustBeSelected'),
}) {
  return {
    type,
    message,
    required: true,
    trigger: 'change',
  };
}

export function createCheckboxRules() {
  return {
    type: 'array',
    required: true,
    message: tm('thisFieldMustBeSelected'),
    trigger: 'change',
    min: 1,
  };
}

export function createMobileRule() {
  return {
    pattern: /^1[3-9][0-9]{9}$/,
    message: tm('mobileNumberFormatIsIncorrect'),
    trigger: 'blur',
  };
}

export function createEmailRule() {
  return {
    type: 'email',
    message: tm('emailFormatIsIncorrect'),
    trigger: 'blur',
  };
}


/** 可选日期时间不早于 当前日期时间 */
export function createDateTimeRule({
  name = '',
}) {
  return {
    required: true,
    validator(rule, value, callback) {
      if (isBefore(value, new Date())) {
        /** {name} 不能小于当前时间 */
        callback(new Error(tm('dateMustBeLaterThanNow', { name })));
      } else {
        callback();
      }
    },
    trigger: 'change',
  };
}
