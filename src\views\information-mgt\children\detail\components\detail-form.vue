<script setup lang="ts">
import DetailLabelItem from '@/components/common/detail-label-item.vue';
import ShowImage from '@/components/biz/attachment/show-image.vue';
import PimaSanitizeHtml from '@/components/common/pima-sanitize-html';

import { namespaceT } from '@/helps/namespace-t';
import { useArticleTypeStore } from '@/store/data-tags/article-type';
import { ArticleDetailType } from '^/types/information-mgt';
import { onBeforeMount } from 'vue';
import { getYoNI18nText } from '@/helps/i18n/y-o-n';
import { isY } from '@/helps/y-o-n';
import { formateToDate } from '@/helps/formate-to-date-type';


const model = defineModel<ArticleDetailType>();


const LABEL_WIDTH = 120;

const t = namespaceT('informationMgt');
const typeStore = useArticleTypeStore();


onBeforeMount(() => {
  typeStore.loadDataIfNeeded();
});

</script>


<template>
  <!-- 标题 -->
  <DetailLabelItem
    :label="t('label.title')"
    :value="model.title"
    :label-width="LABEL_WIDTH"
  />

  <!-- 类型 -->
  <DetailLabelItem
    :label="t('label.type')"
    :value="typeStore.getTextByCode(model.type)"
    :label-width="LABEL_WIDTH"
  />

  <!-- 封面图 -->
  <DetailLabelItem
    v-if="model.cover"
    :label="t('label.coverImg')"
    :label-width="LABEL_WIDTH"
  >
    <ShowImage
      :attachments="model.coverFiles"
      :size="'fit-content'"
    />
  </DetailLabelItem>

  <!-- 是否外链 -->
  <DetailLabelItem
    :label="t('label.isExternalLink')"
    :value="getYoNI18nText(model.urlInd)"
    :label-width="LABEL_WIDTH"
  />

  <!-- 外链 -->

  <DetailLabelItem
    v-if="isY(model.urlInd)"
    :label="t('label.externalLink')"
    :value="model.url"
    :label-width="LABEL_WIDTH"
  />

  <!-- 内容 -->
  <DetailLabelItem
    v-else
    :label="t('label.content')"
    :label-width="LABEL_WIDTH"
  >
    <PimaSanitizeHtml :inner-html="model.content" />
  </DetailLabelItem>

  <!-- 发布时间 -->
  <DetailLabelItem
    :label="t('label.publishTime')"
    :value="formateToDate(model.releaseTime,'dateTime')"
    :label-width="LABEL_WIDTH"
  />
</template>
