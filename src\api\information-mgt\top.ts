import { Common<PERSON>pi, RequestMethod } from '@/api/common/common-api';
import { BaseError } from '@/errors/base-error';
import { namespaceT } from '@/helps/namespace-t';

enum ErrorCodes {
  NO_EXISTS = 'NO_EXISTS',
  STATUS_ABNORMAL = 'STATUS_ABNORMAL',
}

export class TopApi extends CommonApi<void> {
  id:number;

  constructor({ id }) {
    super();
    this.id = id;
  }

  url() {
    return `articles/${this.id}/operate-top`;
  }

  method(): RequestMethod {
    return 'POST';
  }


  async send(): Promise<void> {
    try {
      await super.send();
    } catch (error) {
      const t = namespaceT('apiErrors.common');

      switch (error.code) {
        case ErrorCodes.NO_EXISTS:
        case ErrorCodes.STATUS_ABNORMAL:
          throw new BaseError(t(error.code));
        default:
          throw error;
      }
    }
  }
}
