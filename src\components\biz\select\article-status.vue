<script setup lang="ts">
import { useArticleStatusStore } from '@/store/data-tags/article-status';
import { onBeforeMount } from 'vue';


const store = useArticleStatusStore();

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
