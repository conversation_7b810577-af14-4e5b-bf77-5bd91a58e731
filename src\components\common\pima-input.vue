<script lang="ts" setup>
import { InputType } from '@/consts/input-type';

const props = withDefaults(defineProps<{
  type?: InputType;
  maxlength?: number | null;
  isLimit?:boolean;
}>(), {
  type: InputType.TEXT,
  maxlength: null,
  isLimit: true,
});

const emit = defineEmits([
  'update:modelValue',
]);


const isTextarea = props.type === InputType.TEXTAREA;
const maxLengthByType = isTextarea ? 500 : 255;
const inMaxLength = props.maxlength ? props.maxlength : maxLengthByType;

function getClasses() {
  switch (props.type) {
    case InputType.TEXTAREA:
      return 'pima-input-type-textarea';

    default:
      return 'pima-input';
  }
}

function onInput(val) {
  switch (props.type) {
    case InputType.TEXT:
    case InputType.TEXTAREA:
      emit('update:modelValue', val ? val.target.value.trim() : val);
      break;
    default:
      emit('update:modelValue', val);
  }
}

function onClear() {
  emit('update:modelValue', '');
}
</script>


<template>
  <Input
    :class="getClasses()"
    :type="type"
    :maxlength="isLimit ? inMaxLength : undefined"
    @input="onInput"
    @on-clear="onClear"
  />
</template>
