<script setup lang="ts">
import { ref, reactive, onBeforeMount } from 'vue';
import { useRouter, useRoute } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import DetailForm from './components/detail-form.vue';
import Modals from '../../components/modals.vue';

import { DetailApi } from '@/api/information-mgt/detail';
import { goBack, push } from '@/helps/navigation';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError } from '@/helps/toast';

import { RouterName as RN } from '@/config/router';
import type { WarningModelType } from '@/helps/warning-model';

import { handleDetailData } from '../../helps/handle-api-data';
import { createArticleVO, createCancelReleaseModel, createDeleteModel, createTopModel } from '../../helps/models';
import { isY } from '@/helps/y-o-n';


const router = useRouter();
const route = useRoute();

const t = namespaceT('informationMgt');

const loading = ref(false);
const model = reactive(createArticleVO());
const deleteModel = reactive(createDeleteModel());
const cancelReleaseModel = reactive(createCancelReleaseModel());
const topModel = reactive(createTopModel());


const toEditPage = () => {
  push(router, {
    name: RN.InformationMgtEdit,
    params: { id: route.params.id },
  });
};

const onShowModal = (modal:WarningModelType) => {
  Object.assign(modal, {
    visible: true,
    id: route.params.id,
  });
};


const fetchDetail = async () => {
  try {
    loading.value = true;
    const api = new DetailApi({ id: route.params.id });
    const res = await api.sendWithSpecifyType();
    Object.assign(model, handleDetailData(res));
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
};

onBeforeMount(() => {
  fetchDetail();
});

</script>


<template>
  <TitleBar
    :title="t('title.detail')"
    go-back
    @go-back="goBack(router)"
  />

  <WrapperForm
    :loading="loading"
    class="px-60 mb-80 mt-20"
  >
    <DetailForm
      v-model="model"
    />

    <template
      #action
    >
      <Button
        v-if="$can((P)=>P.InformationMgt.Edit) && isY(model.editableInd)"
        type="primary"
        size="large"
        class="pima-btn mr-15"
        @click="toEditPage"
      >
        {{ t('action.edit') }}
      </Button>

      <Button
        v-if="$can((P)=>P.InformationMgt.Cancel) && isY(model.cancelReleaseableInd)"
        type="primary"
        size="large"
        class="pima-btn mr-15"
        @click="onShowModal(cancelReleaseModel)"
      >
        {{ t('action.cancelRelease') }}
      </Button>

      <Button
        v-if="$can((P)=>P.InformationMgt.Top) && isY(model.topSettingableInd)"
        type="primary"
        size="large"
        class="pima-btn mr-15"
        @click="onShowModal(topModel)"
      >
        {{ t('action.top') }}
      </Button>

      <Button
        v-if="$can((P)=>P.InformationMgt.Remove) && isY(model.deletableInd)"
        type="primary"
        size="large"
        class="pima-btn"
        @click="onShowModal(deleteModel)"
      >
        {{ t('action.delete') }}
      </Button>
    </template>
  </WrapperForm>

  <Modals
    v-model:delete-model="deleteModel"
    v-model:cancel-release-model="cancelReleaseModel"
    v-model:top-model="topModel"
    is-detail
    @on-reload="fetchDetail"
    @on-delete="goBack(router)"
  />
</template>
