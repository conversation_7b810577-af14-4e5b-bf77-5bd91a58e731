
<script setup lang="ts">
import { defineAsyncComponent } from 'vue';

import ClientOnly from '../client-only';

// eslint-disable-next-line import/no-unresolved
const PimaSelectPeopleAssociative = defineAsyncComponent(() => import('pimaRemoteUI/PimaSelectPeopleAssociative'));

</script>

<template>
  <ClientOnly>
    <PimaSelectPeopleAssociative
      v-bind="$attrs"
    />
  </ClientOnly>
</template>


<style lang="less">
.pima-theme .pima-select-people-associative .select-people-associative .header {
  height: auto;
}
</style>
