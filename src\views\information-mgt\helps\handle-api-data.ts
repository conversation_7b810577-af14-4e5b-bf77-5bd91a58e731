import _ from 'lodash';

import type { ArticleDetailType, ArticleDTO, SearchSimpleModelType, TopApiDTO } from '^/types/information-mgt';
import type { PaginationParamsOption } from '@/helps/api';

import { formateToDate } from '@/helps/formate-to-date-type';
import { isY } from '@/helps/y-o-n';
import { createArticleDTO, createTopFormModels } from './models';

type ListParamsType = SearchSimpleModelType & PaginationParamsOption;

/** 处理列表接口参数 */
export const handleListParams = (params:ListParamsType) => {
  const cloneParams = _.cloneDeep(params);

  cloneParams.releaseStartTime = formateToDate(cloneParams.releaseStartTime);
  cloneParams.releaseEndTime = formateToDate(cloneParams.releaseEndTime);


  if (!isY(cloneParams.hotInd)) {
    cloneParams.hotInd = undefined;
  }

  return cloneParams;
};


/** 处理 表单提交数据 */
export const handleSubmitFormData = (data: ArticleDTO) => {
  const cloneData = _.pick(data, Object.keys(createArticleDTO()));

  cloneData.cover = cloneData.coverFiles?.[0]?.id;
  delete cloneData.coverFiles;

  if (isY(cloneData.urlInd)) {
    cloneData.content = undefined;
  } else {
    cloneData.url = undefined;
  }


  if (isY(cloneData.scheduledReleaseInd)) {
    cloneData.releaseTime = formateToDate(cloneData.releaseTime, 'fullDateTime');
  } else {
    delete cloneData.releaseTime;
  }

  return cloneData;
};


/** 处理 详情内容 */
export const handleDetailData = (data: ArticleDetailType) => {
  const cloneData = _.cloneDeep(data);

  Object.assign(cloneData, {
    coverFiles: cloneData.cover ? [cloneData.cover] : [],
    cover: cloneData.cover?.id,
    releaseTime: new Date(cloneData.releaseTime),
  });

  return cloneData;
};


/** 处理置顶 params 参数 */
export const handleTopParams = (params: TopApiDTO) => {
  const cloneParams = _.cloneDeep(params);

  Object.assign(cloneParams, {
    hotStartTime: isY(cloneParams.hotInd) ? formateToDate(cloneParams.hotStartTime) : null,
    hotEndTime: isY(cloneParams.hotInd) ? formateToDate(cloneParams.hotEndTime) : null,
  });


  return cloneParams;
};


/** 处理 详情 获取 置顶模态窗 参数 */
export const handleDetailForTop = (params: ArticleDetailType): TopApiDTO => {
  const cloneParams = _.pick(params, Object.keys(createTopFormModels())) as TopApiDTO;


  return cloneParams;
};
