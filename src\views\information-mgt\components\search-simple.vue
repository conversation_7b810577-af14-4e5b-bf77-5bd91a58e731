<script setup lang="ts">
import type { SearchSimpleModelType } from '^/types/information-mgt';

import WrapperSearchSimple from '@/components/common/wrapper-search-simple.vue';
import PairLabelItem from '@/components/common/pair-label-item.vue';
import InputSearch from '@/components/common/input-search.vue';
import PickDateRange from '@/components/common/picker/date-range.vue';
import SelectArticleStatus from '@/components/biz/select/article-status.vue';
import SelectArticleType from '@/components/biz/select/article-type.vue';
import CheckboxTop from '@/components/biz/checkbox/top.vue';

import { namespaceT } from '@/helps/namespace-t';

const emit = defineEmits<{
  'on-search': [],
}>();

const t = namespaceT('informationMgt');
const tc = namespaceT('common');
const model = defineModel<SearchSimpleModelType>();

const emitSearch = () => {
  emit('on-search');
};
</script>


<template>
  <WrapperSearchSimple>
    <PairLabelItem :label="t('label.type')">
      <SelectArticleType
        v-model="model.type"
        class="w-150 "
        clearable
        @on-change="emitSearch"
      />
    </PairLabelItem>

    <PairLabelItem :label="t('label.publishTime')">
      <PickDateRange
        v-model:min="model.releaseStartTime"
        v-model:max="model.releaseEndTime"
        @on-change="emitSearch"
      />
    </PairLabelItem>

    <PairLabelItem :label="t('label.status')">
      <SelectArticleStatus
        v-model="model.status"
        class="w-150 "
        clearable
        @on-change="emitSearch"
      />
    </PairLabelItem>


    <PairLabelItem no-colon>
      <InputSearch
        v-model.trim="model.keyword"
        :placeholder="tc('placeholder.input')"
        class="w-300"
        clearable
        @on-clear="emitSearch"
        @on-search="emitSearch"
      />
    </PairLabelItem>

    <PairLabelItem no-colon>
      <CheckboxTop
        v-model="model.hotInd"
        @on-change="emitSearch"
      />
    </PairLabelItem>
  </WrapperSearchSimple>
</template>
