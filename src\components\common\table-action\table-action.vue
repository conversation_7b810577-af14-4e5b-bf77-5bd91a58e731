<template>
  <div>
    <TableActionDefault
      v-if="checkShowDefaultOrPoptip"
      :actions="filterActions"
      @trigger="onTrigger"
    />

    <TableActionPoptip
      v-else
      :limit="limit"
      :actions="filterActions"
      :text-btn="textBtn"
      @trigger="onTrigger"
    />
  </div>
</template>


<script lang='ts'>
import _ from 'lodash';
import { computed, defineComponent, toRef, unref } from 'vue';

import TableActionDefault from './table-action-default.vue';
import TableActionPoptip from './table-action-poptip.vue';

export default defineComponent({
  name: 'TableAction',

  components: {
    TableActionDefault,
    TableActionPoptip,
  },

  props: {
    rowData: {
      type: Object,
      default() {
        return {};
      },
    },

    actions: {
      type: Array,
      default() {
        return [];
      },
    },

    limit: {
      type: Number,
      default: 2,
    },

    extendAll: Boolean,

    textBtn: {
      type: Boolean,
      default: false,
    },
  },

  setup(props) {
    const actionsOfProps = toRef(props, 'actions');
    const limitOfProps = toRef(props, 'limit');
    const extendAllOfProps = toRef(props, 'extendAll');
    const rowDataOfProps = toRef(props, 'rowData');

    function onTrigger(triggerEvent) {
      triggerEvent?.(unref(rowDataOfProps));
    }

    function canAction(fn) {
      if (_.isNil(fn)) return true;

      if (_.isFunction(fn)) return fn(unref(rowDataOfProps));

      return fn;
    }

    function disabledAction(fn) {
      if (_.isNil(fn)) return false;

      if (_.isFunction(fn)) return fn(unref(rowDataOfProps));

      return fn;
    }

    const filterActions = computed(() => {
      return unref(actionsOfProps)
        .filter((item) => canAction(item.can))
        .map((i) => ({
          ...i,
          disabled: disabledAction(i.disabled),
        }));
    });

    const checkShowDefaultOrPoptip = computed(() => {
      if (unref(extendAllOfProps)) return true;
      return unref(filterActions).length <= unref(limitOfProps);
    });

    return {
      filterActions,
      checkShowDefaultOrPoptip,

      onTrigger,
    };
  },
});
</script>
