<script setup lang="ts">
import { Ref, isRef } from 'vue';

import WarningModal from '@/components/biz/modal/warning-modal.vue';
import ModalTop from './modal-top.vue';

import type { TopApiDTO } from '^/types/information-mgt';

import { RemoveApi } from '@/api/information-mgt/remove';
import { CancelApi } from '@/api/information-mgt/cancel';
import { TopApi } from '@/api/information-mgt/top';
import { namespaceT } from '@/helps/namespace-t';
import { WarningModelType } from '@/helps/warning-model';
import { openToastError, openToastSuccess } from '@/helps/toast';

import { handleTopParams } from '../helps/handle-api-data';


const tc = namespaceT('common');
const t = namespaceT('informationMgt');

interface Props {
  isDetail?:boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isDetail: false,
});


const emit = defineEmits<{
  'on-reload':[]
  'on-delete':[]
}>();

const deleteModel = defineModel<WarningModelType>('delete-model');
const cancelReleaseModel = defineModel<WarningModelType>('cancel-release-model');
const topModel = defineModel<WarningModelType>('top-model');


const onCloseModal = (modal:Ref<WarningModelType> | WarningModelType) => {
  // eslint-disable-next-line no-param-reassign
  (isRef(modal) ? modal.value : modal).visible = false;
};


const onDelete = async () => {
  try {
    deleteModel.value.loading = true;
    const api = new RemoveApi({ id: deleteModel.value.id });
    await api.send();
    openToastSuccess(tc('hint.successfullyDeleted'));
    onCloseModal(deleteModel);
    if (props.isDetail) {
      emit('on-delete');
    } else {
      emit('on-reload');
    }
  } catch (error) {
    openToastError(error.message);
  } finally {
    deleteModel.value.loading = false;
  }
};


const onCancelRelease = async () => {
  try {
    cancelReleaseModel.value.loading = true;
    const api = new CancelApi({ id: cancelReleaseModel.value.id });
    await api.send();
    openToastSuccess(t('hint.cancelReleaseSuccessfully'));
    onCloseModal(cancelReleaseModel);

    emit('on-reload');
  } catch (error) {
    openToastError(error.message);
  } finally {
    cancelReleaseModel.value.loading = false;
  }
};

const onTop = async (model:TopApiDTO) => {
  try {
    topModel.value.loading = true;
    const api = new TopApi({ id: topModel.value.id });
    api.data = { ...handleTopParams(model) };
    await api.send();
    openToastSuccess(tc('hint.handleSuccess'));
    onCloseModal(topModel);
    emit('on-reload');
  } catch (error) {
    openToastError(error.message);
  } finally {
    topModel.value.loading = false;
  }
};


</script>


<template>
  <WarningModal
    v-model="deleteModel.visible"
    v-bind="deleteModel"
    @on-confirm="onDelete"
    @on-cancel="onCloseModal(deleteModel)"
  />

  <WarningModal
    v-model="cancelReleaseModel.visible"
    v-bind="cancelReleaseModel"
    @on-confirm="onCancelRelease"
    @on-cancel="onCloseModal(cancelReleaseModel)"
  />

  <ModalTop
    v-model="topModel.visible"
    v-bind="topModel"
    @on-confirm="onTop"
    @on-cancel="onCloseModal(topModel)"
  />
</template>
