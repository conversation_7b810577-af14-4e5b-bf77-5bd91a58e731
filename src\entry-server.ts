import URI from 'urijs';

import { BaseError } from '@/errors/base-error';
import { createApp } from './main';


function getStaticResourcesBaseUri() {
  return new URI(process.env.STATIC_RESOURCES_BASE_URL);
}

// Favicon文件地址
function getFaviconUrl() {
  const uri = getStaticResourcesBaseUri();
  uri.segment('favicon.ico');

  return uri.toString();
}

// 主题样式文件地址
function getThemeIViewCssUrl() {
  const uri = getStaticResourcesBaseUri();
  uri.segment('css');
  uri.segment('pima-theme-iview');
  uri.segment(process.env.THEME_IVIEW_VERSION || 'latest');
  uri.segment('default.min.css');
  if (process.env.THEME_IVIEW_FLAG) {
    uri.search(process.env.THEME_IVIEW_FLAG);
  }

  return uri.toString();
}

// iconfont样式文件地址
function getIconfontCssUrl() {
  const uri = getStaticResourcesBaseUri();
  uri.segment('iconfont');
  uri.segment(process.env.ICONFONT_VERSION || 'latest');
  uri.segment('iconfont.css');
  if (process.env.ICONFONT_FLAG) {
    uri.search(process.env.ICONFONT_FLAG);
  }

  return uri.toString();
}

function getLang(locale) {
  return locale.includes('en') ? 'en' : 'zh';
}


export default (ctx) => {
  return new Promise((resolve, reject) => {
    const { app, router, pinia, i18n } = createApp({
      locale: ctx.appLocale,
      loggedIn: ctx.appLoggedIn,
    });

    router.push(ctx.url);
    router.isReady().then(() => {
      const { matched } = router.currentRoute.value;
      if (matched.length === 0) {
        const err = new BaseError('Router has not matched compoinents found');
        err.code = 'ROUTE_NOT_FOUND';
        reject(err);
        return;
      }

      // 如果在路由配置里 meta.requiresAuth 为 false，则为当前路由可不通过认证访问，反之亦然
      // meta.requiresAuth 不配置或配置为 true，则为需要认证
      // 当前用户的认证状态保存于 ctx.appLoggedIn，该值由server端维护
      // 当当前访问地址带有 ticket 时，需要进行放行，该参数为 CAS 认证成功后返回值
      if (!router.currentRoute.value.query?.ticket
        && router.currentRoute.value.meta?.requiresAuth !== false
        && ctx.appLoggedIn === false) {
        const err = new BaseError('Login required');
        err.code = 'LOGIN_REQUIRED';
        reject(err);
        return;
      }

      // 如果URL中带有 login 参数，且当前用户未登录，则需要进行登录
      if ('login' in router.currentRoute.value.query
        && ctx.appLoggedIn === false) {
        const err = new BaseError('Login required');
        err.code = 'LOGIN_REQUIRED';
        reject(err);
        return;
      }


      Object.assign(ctx, {
        // @ts-expect-error assume locale is a ref
        lang: getLang(i18n.global.locale.value),
        title: i18n.global.t('title'),
        // state 生成脚本 window.__INITIAL_STATE__
        state: pinia.state.value,
        enableJavaScriptTips: i18n.global.t('enableJavaScriptTips'),
        meta: `<link rel="icon" type="image/x-icon" href="${getFaviconUrl()}">
    <link rel="stylesheet" href="${getThemeIViewCssUrl()}">
    <link rel="stylesheet" href="${getIconfontCssUrl()}">`,
      });

      resolve(app);
    }).catch(reject);
  });
};
