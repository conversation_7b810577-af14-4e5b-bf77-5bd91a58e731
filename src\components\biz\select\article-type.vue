<script setup lang="ts">
import { useArticleTypeStore } from '@/store/data-tags/article-type';
import { onBeforeMount } from 'vue';


const store = useArticleTypeStore();

onBeforeMount(() => {
  store.loadDataIfNeeded();
});

</script>


<template>
  <Select
    class="pima-select"
    v-bind="$attrs"
  >
    <Option
      v-for="item in store.data"
      :key="item.code"
      :value="item.code"
      :label="item.nameByLocale"
    />
  </Select>
</template>
