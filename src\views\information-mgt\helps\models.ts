import type { ArticleDTO, ArticleVO, SearchSimpleModelType, TopApiDTO } from '^/types/information-mgt';

import { YoN } from '@/consts/y-o-n';
import { createWarningModel } from '@/helps/warning-model';
import { namespaceT } from '@/helps/namespace-t';

export const createSearchSimpleModel = (): SearchSimpleModelType => {
  return {
    keyword: undefined,
    hotInd: undefined,
    releaseStartTime: undefined,
    releaseEndTime: undefined,
    status: undefined,
    type: undefined,
  };
};


export const createArticleDTO = (): ArticleDTO => {
  return {
    title: undefined,
    type: undefined,
    cover: undefined,
    coverFiles: [],
    urlInd: YoN.N,
    url: undefined,
    content: undefined,
    scheduledReleaseInd: YoN.N,
    releaseTime: undefined,
  };
};


export const createArticleVO = (): ArticleVO => {
  return {
    id: undefined,
    title: undefined,
    type: undefined,
    releaseTime: undefined,
    status: undefined,
    updTime: undefined,
    updUserName: undefined,
    cancelReleaseableInd: undefined,
    topSettingableInd: undefined,
    deletableInd: undefined,
    editableInd: undefined,
    hotInd: undefined,
  };
};


/** 模态窗 - 删除 */
export const createDeleteModel = () => {
  const t = namespaceT('informationMgt.modal.delete');

  return createWarningModel({
    title: t('title'),
    content: t('content'),
  });
};

/** 模态窗 -  取消发布 */
export const createCancelReleaseModel = () => {
  const t = namespaceT('informationMgt.modal.cancelRelease');

  return createWarningModel({
    title: t('title'),
    content: t('content'),
  });
};

/** 模态窗 - 置顶 */
export const createTopModel = () => {
  const t = namespaceT('informationMgt.modal.top');

  return createWarningModel({
    title: t('title'),
  });
};

/** 模态窗- 置顶- 表单 */
export const createTopFormModels = ():TopApiDTO => {
  return {
    hotInd: YoN.Y,
    hotStartTime: undefined,
    hotEndTime: undefined,
  };
};
