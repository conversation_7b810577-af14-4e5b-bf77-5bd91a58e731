import { YoN } from '@/consts/y-o-n';
import { AttachmentVO } from './attachment';

export interface SearchSimpleModelType {
  /**
     * 是否热点  界面:置顶 枚举：[置顶：Y，不置顶：N]
     */
  hotInd?: string;
  /**
     * 关键字
     */
  keyword?: string;
  /**
     * 发布范围结束时间 yyyy-MM-dd
     */
  releaseEndTime?: string;
  /**
     * 发布范围开始时间 yyyy-MM-dd
     */
  releaseStartTime?: string;
  /**
     * 状态 数据字典：ARTICLE_STATUS
     */
  status?: string;
  /**
     * 类型 数据字典：ARTICLE_TYPE
     */
  type?: string;
}

/**
 * ArticleVO，资讯VO
 */
export interface ArticleVO {
  /**
     * 是否可取消发布 枚举[是：Y，否：N]
     */
  cancelReleaseableInd?: string;
  /**
     * 是否可删除 枚举[是：Y，否：N]
     */
  deletableInd?: string;
  /**
     * 是否可编辑 枚举[是：Y，否：N]
     */
  editableInd?: string;
  /**
     * 是否热点  界面:置顶
     */
  hotInd?: string;
  /**
     * 主键ID
     */
  id?: number;
  /**
     * 发布时间
     */
  releaseTime?: string;
  /**
     * 状态 数据字典：ARTICLE_STATUS
     */
  status?: string;
  /**
     * 标题
     */
  title?: string;
  /**
     * 是否可置顶 枚举[是：Y，否：N]
     */
  topSettingableInd?: string;
  /**
     * 类型 数据字典：ARTICLE_TYPE
     */
  type?: string;
  /**
     * 操作时间
     */
  updTime?: string;
  /**
     * 操作人
     */
  updUserName?: string;

  /**
     * 设置结束时间 yyyy-MM-dd
     */
  hotEndTime?: string;
  /**
     * 热点开始时间 yyyy-MM-dd
     */
  hotStartTime?: string;
}


export type InformationMgtListItemType = ArticleVO;

export type ArticleDetailType = ArticleVO & {
  cover?: AttachmentVO;
  coverFiles?: AttachmentVO[];
  /**
     * 是否外链 枚举[是：Y，否：N]
     */
  urlInd?: YoN;
  /**
     * 外链
     */
  url?: string;
  /**
     * 内容
     */
  content?: string;
};


export interface ArticleDTO {
  /**
     * 内容 是否外链为N时必填
     */
  content?: string;
  /**
     * 封面图
     */
  cover?: number;

  coverFiles? : AttachmentVO[];
  /**
     * 发布时间 yyyy-MM-dd HH:mm:ss 是否定时发布为Y时必填
     */
  releaseTime?: string;
  /**
     * 是否定时发布 枚举[是：Y，否：N]
     */
  scheduledReleaseInd: Ind;
  /**
     * 标题
     */
  title: string;
  /**
     * 类型 数据字典：ARTICLE_TYPE
     */
  type: string;
  /**
     * 外链 是否外链为Y时必填
     */
  url?: string;
  /**
     * 是否外链 枚举[是：Y，否：N]
     */
  urlInd: Ind;
}


export interface TopApiDTO {
  /**
     * 设置结束时间 yyyy-MM-dd
     */
  hotEndTime: string;
  /**
     * 是否热点  界面:置顶 枚举[是：Y，否：N]
     */
  hotInd: string;
  /**
     * 热点开始时间 yyyy-MM-dd
     */
  hotStartTime: string;
}
